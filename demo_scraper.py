#!/usr/bin/env python3
"""
Demo script showing how to use the VLR scraper programmatically
"""

import requests
from bs4 import BeautifulSoup
import json
import time
from datetime import datetime

class VLRScraperDemo:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
    
    def scrape_event_basic(self, event_url):
        """
        Basic scraping example - get event info and matches
        """
        print(f"🎯 Scraping: {event_url}")
        print("=" * 60)
        
        # Get event information
        print("📋 Fetching event information...")
        event_info = self.get_event_info(event_url)
        
        # Get matches
        matches_url = event_url.replace('/event/', '/event/matches/')
        print("🏆 Fetching matches...")
        matches = self.get_matches(matches_url)
        
        # Display results
        self.display_results(event_info, matches)
        
        return {
            'event_info': event_info,
            'matches': matches
        }
    
    def get_event_info(self, url):
        """Get basic event information"""
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        event_info = {'url': url}
        
        # Event title
        title_elem = soup.find('h1', class_='wf-title')
        if title_elem:
            event_info['title'] = title_elem.get_text(strip=True)
        
        # Event description (contains dates and location)
        desc_elem = soup.find('div', class_='event-desc')
        if desc_elem:
            desc_text = desc_elem.get_text(strip=True)
            event_info['description'] = desc_text
            
            # Extract dates and location
            lines = desc_text.split('\n')
            for line in lines:
                line = line.strip()
                if any(month in line for month in ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                                                  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']):
                    event_info['dates'] = line
                elif any(word in line.lower() for word in ['korea', 'spain', 'china', 'usa', 'brazil']):
                    event_info['location'] = line
        
        return event_info
    
    def get_matches(self, matches_url):
        """Get all matches from the event"""
        response = requests.get(matches_url, headers=self.headers)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        matches = []
        match_containers = soup.find_all('a', class_='wf-module-item')
        
        for container in match_containers:
            match_data = self.extract_match_data(container)
            if match_data:
                matches.append(match_data)
        
        return matches
    
    def extract_match_data(self, container):
        """Extract match data from container"""
        try:
            # Team names
            team_elements = container.find_all('div', class_='match-item-vs-team-name')
            if len(team_elements) < 2:
                return None
            
            team1 = team_elements[0].get_text(strip=True)
            team2 = team_elements[1].get_text(strip=True)
            
            # Scores
            score_elements = container.find_all('div', class_='match-item-vs-team-score')
            score1 = score_elements[0].get_text(strip=True) if len(score_elements) > 0 else ''
            score2 = score_elements[1].get_text(strip=True) if len(score_elements) > 1 else ''
            
            # Stage
            event_elements = container.find_all('div', class_='match-item-event')
            stage = event_elements[0].get_text(strip=True) if event_elements else 'Unknown'
            
            # Time
            time_elements = container.find_all('div', class_='match-item-time')
            match_time = time_elements[0].get_text(strip=True) if time_elements else 'TBD'
            
            # Status
            status = 'Completed' if score1 and score2 and score1.isdigit() and score2.isdigit() else 'Scheduled'
            
            return {
                'team1': team1,
                'team2': team2,
                'score1': score1,
                'score2': score2,
                'stage': stage,
                'time': match_time,
                'status': status,
                'match_url': 'https://www.vlr.gg' + container.get('href', '')
            }
            
        except Exception as e:
            print(f"Error extracting match: {e}")
            return None
    
    def display_results(self, event_info, matches):
        """Display the scraped results"""
        print("\n🏆 EVENT INFORMATION")
        print("-" * 40)
        
        if 'title' in event_info:
            print(f"📋 Title: {event_info['title']}")
        if 'dates' in event_info:
            print(f"📅 Dates: {event_info['dates']}")
        if 'location' in event_info:
            print(f"📍 Location: {event_info['location']}")
        
        print(f"\n🏆 MATCHES ({len(matches)} total)")
        print("-" * 40)
        
        # Group matches by status
        completed = [m for m in matches if m['status'] == 'Completed']
        scheduled = [m for m in matches if m['status'] == 'Scheduled']
        
        print(f"✅ Completed: {len(completed)}")
        print(f"⏰ Scheduled: {len(scheduled)}")
        
        # Show some completed matches
        print(f"\n📊 Recent Completed Matches:")
        for match in completed[:5]:
            score = f"({match['score1']}-{match['score2']})" if match['score1'] and match['score2'] else ""
            print(f"  • {match['team1']} vs {match['team2']} {score}")
            print(f"    Stage: {match['stage']} | Time: {match['time']}")
        
        # Show team statistics
        self.show_team_stats(completed)
    
    def show_team_stats(self, completed_matches):
        """Show basic team statistics"""
        if not completed_matches:
            return
        
        print(f"\n📈 TEAM PERFORMANCE")
        print("-" * 40)
        
        team_stats = {}
        
        for match in completed_matches:
            team1, team2 = match['team1'], match['team2']
            score1, score2 = match['score1'], match['score2']
            
            if score1.isdigit() and score2.isdigit():
                s1, s2 = int(score1), int(score2)
                
                # Initialize teams
                for team in [team1, team2]:
                    if team not in team_stats:
                        team_stats[team] = {'wins': 0, 'losses': 0, 'maps_won': 0, 'maps_lost': 0}
                
                # Update stats
                team_stats[team1]['maps_won'] += s1
                team_stats[team1]['maps_lost'] += s2
                team_stats[team2]['maps_won'] += s2
                team_stats[team2]['maps_lost'] += s1
                
                if s1 > s2:
                    team_stats[team1]['wins'] += 1
                    team_stats[team2]['losses'] += 1
                else:
                    team_stats[team2]['wins'] += 1
                    team_stats[team1]['losses'] += 1
        
        # Sort teams by win rate
        sorted_teams = sorted(team_stats.items(), 
                            key=lambda x: x[1]['wins'] / (x[1]['wins'] + x[1]['losses']) if (x[1]['wins'] + x[1]['losses']) > 0 else 0, 
                            reverse=True)
        
        for i, (team, stats) in enumerate(sorted_teams[:10], 1):
            total_matches = stats['wins'] + stats['losses']
            win_rate = (stats['wins'] / total_matches * 100) if total_matches > 0 else 0
            map_diff = stats['maps_won'] - stats['maps_lost']
            
            print(f"{i:2d}. {team}")
            print(f"    Record: {stats['wins']}-{stats['losses']} ({win_rate:.1f}% win rate)")
            print(f"    Maps: {stats['maps_won']}-{stats['maps_lost']} ({map_diff:+d})")
    
    def save_data(self, data, filename):
        """Save data to JSON file"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"\n💾 Data saved to {filename}")

def main():
    """Demo the scraper with different events"""
    scraper = VLRScraperDemo()
    
    # Example events to scrape
    events = [
        {
            'name': 'Champions 2024',
            'url': 'https://www.vlr.gg/event/2097/valorant-champions-2024'
        },
        {
            'name': 'Masters Madrid 2024',
            'url': 'https://www.vlr.gg/event/1921/champions-tour-2024-masters-madrid'
        }
    ]
    
    print("🎮 VLR.gg Scraper Demo")
    print("=" * 60)
    
    for event in events:
        try:
            print(f"\n🔄 Processing {event['name']}...")
            data = scraper.scrape_event_basic(event['url'])
            
            # Save data
            filename = f"{event['name'].lower().replace(' ', '_')}_data.json"
            scraper.save_data(data, filename)
            
            # Small delay between events
            time.sleep(2)
            
        except Exception as e:
            print(f"❌ Error scraping {event['name']}: {e}")
            continue
    
    print(f"\n✅ Demo completed!")
    print("💡 Tip: Use vlr_scraper_ui.py for the full GUI experience with player statistics!")

if __name__ == "__main__":
    main()
