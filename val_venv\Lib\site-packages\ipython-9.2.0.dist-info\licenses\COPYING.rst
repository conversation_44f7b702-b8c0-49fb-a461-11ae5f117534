=============================
 The IPython licensing terms
=============================

IPython is licensed under the terms of the Modified BSD License (also known as
New or Revised or 3-Clause BSD). See the LICENSE file.


About the IPython Development Team
----------------------------------

<PERSON> began IPython in 2001 based on code from <PERSON><PERSON>
<<EMAIL>> and <PERSON> <<EMAIL>>.  <PERSON> is still
the project lead.

The IPython Development Team is the set of all contributors to the IPython
project.  This includes all of the IPython subprojects. 

The core team that coordinates development on GitHub can be found here:
https://github.com/ipython/.

Our Copyright Policy
--------------------

IPython uses a shared copyright model. Each contributor maintains copyright
over their contributions to IPython. But, it is important to note that these
contributions are typically only changes to the repositories. Thus, the IPython
source code, in its entirety is not the copyright of any single person or
institution.  Instead, it is the collective copyright of the entire IPython
Development Team.  If individual contributors want to maintain a record of what
changes/contributions they have specific copyright on, they should indicate
their copyright in the commit message of the change, when they commit the
change to one of the IPython repositories.

With this in mind, the following banner should be used in any source code file 
to indicate the copyright and license terms:

::

    # Copyright (c) IPython Development Team.
    # Distributed under the terms of the Modified BSD License.
