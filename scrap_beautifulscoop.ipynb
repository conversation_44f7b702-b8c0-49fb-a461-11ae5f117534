# pip install requests beautifulsoup4

import requests
import bs4
from bs4 import BeautifulSoup


url = 'https://www.vlr.gg/'
headers = { 'User-Agent' : 'Mozilla/5.0'
}

response = requests.get(url,headers = headers)
soup = BeautifulSoup(response.text,'html.parser')Z

match_cards = soup.find_all('a',class_='wf-card match-item')

for match in match_cards:
    teams = match.find_all('div', class_='match-item-vs-team-name')
    event = match.find('div',class_='match-item-event')
    time = match.find('div',class_='match-item-time')

    if teams and event and time:
        team1 = teams[0].text.strip()
        team2 = teams[1].text.strip()
        event_name = event.text.strip()
        match_time = time.text.strip()

        print(f"{team1} vs {team2}")
        print(f"Event :{event_name}")
        print(f"Time :{match_time}")
        print('_' * 40)





