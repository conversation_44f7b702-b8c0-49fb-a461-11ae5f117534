# 🎮 VLR.gg Event Data Scraper

A comprehensive GUI application for scraping detailed match and player statistics from VLR.gg Valorant tournament events.

## ✨ Features

### 🔍 **Data Collection**
- **Event Information**: Tournament details, dates, location, prize pool
- **Match Data**: Team matchups, scores, stages, timestamps, status
- **Player Statistics**: ACS, K/D/A, ADR, HS%, First Kills/Deaths, and more
- **Team Analytics**: Aggregated team performance metrics

### 🖥️ **User Interface**
- **Modern GUI**: Clean, intuitive interface built with tkinter
- **Real-time Progress**: Live progress tracking during scraping
- **Tabbed Display**: Organized data views for different information types
- **URL Validation**: Automatic validation of VLR.gg event URLs

### 📊 **Data Export**
- **JSON Export**: Complete data in structured JSON format
- **CSV Export**: Separate CSV files for matches, players, and teams
- **Clipboard Copy**: Quick summary copying for sharing

### ⚡ **Performance**
- **Threaded Scraping**: Non-blocking UI during data collection
- **Rate Limiting**: Respectful scraping with delays between requests
- **Error Handling**: Robust error handling and user feedback

## 🚀 Quick Start

### Prerequisites
```bash
pip install requests beautifulsoup4 tkinter
```

### Running the Application
```bash
python vlr_scraper_ui.py
```

## 📖 How to Use

### 1. **Enter Event URL**
- Paste a VLR.gg event URL (e.g., `https://www.vlr.gg/event/2097/valorant-champions-2024`)
- Click "Validate URL" to verify the link

### 2. **Start Scraping**
- Click "🚀 Start Scraping" to begin data collection
- Monitor progress in the progress bar and status messages
- Use "⏹️ Stop" to cancel scraping if needed

### 3. **View Results**
Navigate through the tabs to view different data:

#### 📅 **Event Info Tab**
- Tournament name, dates, location
- Prize pool information
- Event description

#### 🏆 **Matches Tab**
- All tournament matches in a sortable table
- Team names, scores, stages, dates, status
- Click column headers to sort data

#### 👤 **Player Stats Tab**
- Individual player performance statistics
- ACS, K/D/A ratios, ADR, headshot percentage
- First kills/deaths data

#### 🏅 **Team Stats Tab**
- Aggregated team performance metrics
- Total kills, deaths, assists
- Average statistics per match
- Team rankings by K/D ratio

### 4. **Export Data**
- **💾 Export JSON**: Save complete dataset in JSON format
- **📊 Export CSV**: Save data as separate CSV files
- **📋 Copy Summary**: Copy quick summary to clipboard

## 🔧 Technical Details

### **Supported URLs**
The scraper works with VLR.gg event URLs in this format:
```
https://www.vlr.gg/event/{event_id}/{event_name}
```

Examples:
- `https://www.vlr.gg/event/2097/valorant-champions-2024`
- `https://www.vlr.gg/event/1921/champions-tour-2024-masters-madrid`
- `https://www.vlr.gg/event/1999/champions-tour-2024-masters-shanghai`

### **Data Structure**

#### Event Information
```json
{
  "title": "VALORANT Champions 2024",
  "dates": "Aug 1 - 25, 2024",
  "location": "Seoul & Incheon, South Korea",
  "prize_pool": "$1,000,000 USD",
  "url": "https://www.vlr.gg/event/2097/valorant-champions-2024"
}
```

#### Match Data
```json
{
  "team1": "Team Heretics",
  "team2": "EDward Gaming",
  "score1": "2",
  "score2": "3",
  "stage": "Grand FinalPlayoffs",
  "time": "12:30 PM",
  "status": "Completed",
  "match_url": "https://www.vlr.gg/378829/..."
}
```

#### Player Statistics
```json
{
  "player": "ZmjjKK",
  "team": "EDward Gaming",
  "acs": "267",
  "kills": "23",
  "deaths": "15",
  "assists": "4",
  "kd_ratio": "1.53",
  "adr": "178",
  "hs_percent": "24%",
  "first_kills": "3",
  "first_deaths": "2"
}
```

## ⚠️ Important Notes

### **Rate Limiting**
- The scraper includes delays between requests to be respectful to VLR.gg servers
- Player statistics are limited to the first 10 completed matches to avoid overloading
- Each match page request includes a 1-second delay

### **Data Accuracy**
- Data is scraped in real-time from VLR.gg
- Some statistics may be unavailable for certain matches
- Player stats depend on VLR.gg's data availability

### **Error Handling**
- Invalid URLs are detected and reported
- Network errors are handled gracefully
- Missing data fields are handled with default values

## 🛠️ Troubleshooting

### **Common Issues**

1. **"Invalid URL format"**
   - Ensure you're using a VLR.gg event URL
   - Check that the URL follows the correct format

2. **"Connection error"**
   - Check your internet connection
   - VLR.gg might be temporarily unavailable

3. **"No player statistics found"**
   - Some matches may not have detailed stats available
   - Try a different event with completed matches

4. **Application freezing**
   - Use the "Stop" button to cancel long-running operations
   - Restart the application if needed

### **Performance Tips**
- Close other applications to free up memory during large scraping operations
- Use a stable internet connection for best results
- Allow sufficient time for scraping large tournaments

## 📝 Example Workflows

### **Analyzing a Tournament**
1. Find a VLR.gg event URL for a completed tournament
2. Enter the URL and start scraping
3. Review event information and match results
4. Analyze player performance in the Player Stats tab
5. Compare team performance in the Team Stats tab
6. Export data for further analysis

### **Comparing Events**
1. Scrape multiple events separately
2. Export each dataset as JSON
3. Use external tools to compare statistics across tournaments

## 🔮 Future Enhancements

Potential improvements for future versions:
- Support for multiple events in one session
- Advanced filtering and sorting options
- Data visualization charts and graphs
- Historical data comparison
- Automated tournament tracking
- API integration for real-time updates

## 📄 License

This tool is for educational and research purposes. Please respect VLR.gg's terms of service and use responsibly.

---

**Happy Scraping! 🎯**
