import json
from datetime import datetime

def load_and_display_vct_data():
    """Load and display VCT 2024 data in a readable format"""
    
    try:
        with open('vct_2024_matches.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("🎮 VCT 2024 TOURNAMENT DATA")
        print("=" * 80)
        print(f"📊 Total Matches: {data['total_matches']}")
        print(f"🕒 Scraped At: {data['scraped_at']}")
        print(f"🏆 Events: {', '.join(data['events_scraped'])}")
        print()
        
        # Group matches by event
        events = {}
        for match in data['matches']:
            event = match['event']
            if event not in events:
                events[event] = []
            events[event].append(match)
        
        # Display each event
        for event_name, matches in events.items():
            print(f"\n🏆 {event_name.upper()}")
            print(f"📅 {matches[0]['event_dates']}")
            print(f"📍 {matches[0]['location']}")
            print("=" * 80)
            
            # Group by stage
            stages = {}
            for match in matches:
                stage = match['stage']
                if stage not in stages:
                    stages[stage] = []
                stages[stage].append(match)
            
            # Display matches by stage
            for stage_name, stage_matches in stages.items():
                print(f"\n📋 {stage_name}")
                print("-" * 60)
                
                for match in stage_matches:
                    score_display = ""
                    if match['score1'] and match['score2']:
                        score_display = f" ({match['score1']}-{match['score2']})"
                    
                    status_emoji = "✅" if match['status'] == 'Completed' else "⏰"
                    
                    print(f"{status_emoji} {match['team1']} vs {match['team2']}{score_display}")
                    print(f"   🕒 {match['time']} | 🔗 {match['match_url']}")
                    print()
        
        # Summary statistics
        print("\n📈 TOURNAMENT STATISTICS")
        print("=" * 80)
        
        for event_name, matches in events.items():
            print(f"\n🏆 {event_name}:")
            print(f"   📊 Total Matches: {len(matches)}")
            
            # Count completed vs scheduled
            completed = sum(1 for m in matches if m['status'] == 'Completed')
            scheduled = len(matches) - completed
            print(f"   ✅ Completed: {completed}")
            print(f"   ⏰ Scheduled: {scheduled}")
            
            # Most common teams
            team_counts = {}
            for match in matches:
                for team in [match['team1'], match['team2']]:
                    team_counts[team] = team_counts.get(team, 0) + 1
            
            top_teams = sorted(team_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            print(f"   🔥 Most Active Teams:")
            for team, count in top_teams:
                print(f"      • {team}: {count} matches")
        
        print(f"\n✨ Data successfully loaded and displayed!")
        
    except FileNotFoundError:
        print("❌ Error: vct_2024_matches.json not found. Please run the scraper first.")
    except json.JSONDecodeError:
        print("❌ Error: Invalid JSON format in vct_2024_matches.json")
    except Exception as e:
        print(f"❌ Error loading data: {e}")

if __name__ == "__main__":
    load_and_display_vct_data()
