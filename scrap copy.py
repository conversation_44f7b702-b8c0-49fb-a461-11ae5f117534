import sys
print(f"Python path: {sys.executable}")
print(f"Python version: {sys.version}")

try:
    import bs4
    from bs4 import BeautifulSoup
    print(f"BeautifulSoup version: {bs4.__version__}")
except ImportError as e:
    print(f"Import error: {e}")

import requests
from bs4 import BeautifulSoup

url = 'https://www.vlr.gg/matches'
headers = {
    'User-Agent': 'Mozilla/5.0'
}

response = requests.get(url, headers=headers)
print(f"Response status code: {response.status_code}")
print(f"Response content length: {len(response.text)}")

soup = BeautifulSoup(response.text, 'html.parser')

match_cards = soup.find_all('a', class_='wf-module-item')
print(f"Found {len(match_cards)} match cards")

for match in match_cards:
    teams = match.find_all('div', class_='match-item-vs-team-name')
    event = match.find('div', class_='match-item-event')
    time = match.find('div', class_='match-item-time')

    if teams and event and time:
        team1 = teams[0].text.strip()
        team2 = teams[1].text.strip()
        event_name = event.text.strip()
        match_time = time.text.strip()

        print(f"{team1} vs {team2}")
        print(f"Event: {event_name}")
        print(f"Time: {match_time}")
        print('-' * 40)