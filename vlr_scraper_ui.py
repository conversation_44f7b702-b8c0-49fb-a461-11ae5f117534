import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import requests
from bs4 import BeautifulSoup
import json
import csv
import threading
import time
import re
from datetime import datetime
from urllib.parse import urlparse
import os

class VLRScraperUI:
    def __init__(self, root):
        self.root = root
        self.root.title("VLR.gg Event Scraper")
        self.root.geometry("1000x700")
        self.root.configure(bg='#2c3e50')

        # Data storage
        self.scraped_data = {
            'event_info': {},
            'matches': [],
            'player_stats': [],
            'team_stats': []
        }

        # Scraping state
        self.is_scraping = False

        self.setup_ui()

    def setup_ui(self):
        """Setup the main UI components"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Title
        title_label = ttk.Label(main_frame, text="🎮 VLR.gg Event Data Scraper",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # URL Input Section
        self.setup_url_input(main_frame)

        # Control Buttons
        self.setup_control_buttons(main_frame)

        # Progress Section
        self.setup_progress_section(main_frame)

        # Results Display
        self.setup_results_display(main_frame)

        # Export Section
        self.setup_export_section(main_frame)

    def setup_url_input(self, parent):
        """Setup URL input section"""
        url_frame = ttk.LabelFrame(parent, text="Event URL Input", padding="10")
        url_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        url_frame.columnconfigure(1, weight=1)

        ttk.Label(url_frame, text="VLR.gg Event URL:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.url_var = tk.StringVar()
        self.url_entry = ttk.Entry(url_frame, textvariable=self.url_var, width=60)
        self.url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        # Example URL
        example_url = "https://www.vlr.gg/event/2097/valorant-champions-2024"
        self.url_var.set(example_url)

        # Validate button
        self.validate_btn = ttk.Button(url_frame, text="Validate URL", command=self.validate_url)
        self.validate_btn.grid(row=0, column=2)

        # URL status
        self.url_status = ttk.Label(url_frame, text="", foreground="green")
        self.url_status.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))

    def setup_control_buttons(self, parent):
        """Setup control buttons"""
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=2, column=0, columnspan=3, pady=(0, 10))

        self.scrape_btn = ttk.Button(control_frame, text="🚀 Start Scraping",
                                    command=self.start_scraping, style='Accent.TButton')
        self.scrape_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_btn = ttk.Button(control_frame, text="⏹️ Stop",
                                  command=self.stop_scraping, state='disabled')
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.clear_btn = ttk.Button(control_frame, text="🗑️ Clear Data",
                                   command=self.clear_data)
        self.clear_btn.pack(side=tk.LEFT)

    def setup_progress_section(self, parent):
        """Setup progress tracking section"""
        progress_frame = ttk.LabelFrame(parent, text="Scraping Progress", padding="10")
        progress_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                          maximum=100, length=400)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        # Status text
        self.status_var = tk.StringVar(value="Ready to scrape...")
        self.status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        self.status_label.grid(row=1, column=0, sticky=tk.W)

        # Statistics
        self.stats_var = tk.StringVar(value="")
        self.stats_label = ttk.Label(progress_frame, textvariable=self.stats_var,
                                    foreground="blue")
        self.stats_label.grid(row=2, column=0, sticky=tk.W)

    def setup_results_display(self, parent):
        """Setup results display section"""
        results_frame = ttk.LabelFrame(parent, text="Scraped Data", padding="10")
        results_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)

        # Notebook for different data views
        self.notebook = ttk.Notebook(results_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Event Info Tab
        self.setup_event_info_tab()

        # Matches Tab
        self.setup_matches_tab()

        # Player Stats Tab
        self.setup_player_stats_tab()

        # Team Stats Tab
        self.setup_team_stats_tab()

    def setup_event_info_tab(self):
        """Setup event information tab"""
        event_frame = ttk.Frame(self.notebook)
        self.notebook.add(event_frame, text="📅 Event Info")

        self.event_text = scrolledtext.ScrolledText(event_frame, height=15, width=80)
        self.event_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def setup_matches_tab(self):
        """Setup matches tab"""
        matches_frame = ttk.Frame(self.notebook)
        self.notebook.add(matches_frame, text="🏆 Matches")

        # Treeview for matches
        columns = ('Team 1', 'Score', 'Team 2', 'Stage', 'Date', 'Status')
        self.matches_tree = ttk.Treeview(matches_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.matches_tree.heading(col, text=col)
            self.matches_tree.column(col, width=120)

        # Scrollbar for matches
        matches_scrollbar = ttk.Scrollbar(matches_frame, orient=tk.VERTICAL, command=self.matches_tree.yview)
        self.matches_tree.configure(yscrollcommand=matches_scrollbar.set)

        self.matches_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        matches_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

    def setup_player_stats_tab(self):
        """Setup player statistics tab"""
        players_frame = ttk.Frame(self.notebook)
        self.notebook.add(players_frame, text="👤 Player Stats")

        # Treeview for player stats
        columns = ('Player', 'Team', 'ACS', 'K', 'D', 'A', 'K/D', 'ADR', 'HS%', 'FK', 'FD')
        self.players_tree = ttk.Treeview(players_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.players_tree.heading(col, text=col)
            self.players_tree.column(col, width=80)

        # Scrollbar for players
        players_scrollbar = ttk.Scrollbar(players_frame, orient=tk.VERTICAL, command=self.players_tree.yview)
        self.players_tree.configure(yscrollcommand=players_scrollbar.set)

        self.players_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        players_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

    def setup_team_stats_tab(self):
        """Setup team statistics tab"""
        teams_frame = ttk.Frame(self.notebook)
        self.notebook.add(teams_frame, text="🏅 Team Stats")

        self.team_text = scrolledtext.ScrolledText(teams_frame, height=15, width=80)
        self.team_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def setup_export_section(self, parent):
        """Setup export section"""
        export_frame = ttk.LabelFrame(parent, text="Export Data", padding="10")
        export_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E))

        ttk.Button(export_frame, text="💾 Export JSON",
                  command=self.export_json).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(export_frame, text="📊 Export CSV",
                  command=self.export_csv).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(export_frame, text="📋 Copy Summary",
                  command=self.copy_summary).pack(side=tk.LEFT)

    def validate_url(self):
        """Validate the entered URL"""
        url = self.url_var.get().strip()

        if not url:
            self.url_status.config(text="❌ Please enter a URL", foreground="red")
            return False

        # Check if it's a valid VLR.gg event URL
        if not re.match(r'https?://www\.vlr\.gg/event/\d+/', url):
            self.url_status.config(text="❌ Invalid VLR.gg event URL format", foreground="red")
            return False

        try:
            response = requests.head(url, timeout=10)
            if response.status_code == 200:
                self.url_status.config(text="✅ Valid URL - Ready to scrape", foreground="green")
                return True
            else:
                self.url_status.config(text=f"❌ URL returned status {response.status_code}", foreground="red")
                return False
        except requests.RequestException as e:
            self.url_status.config(text=f"❌ Connection error: {str(e)[:50]}...", foreground="red")
            return False

    def start_scraping(self):
        """Start the scraping process"""
        if not self.validate_url():
            return

        if self.is_scraping:
            messagebox.showwarning("Warning", "Scraping is already in progress!")
            return

        # Clear previous data
        self.clear_data()

        # Update UI state
        self.is_scraping = True
        self.scrape_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.progress_var.set(0)
        self.status_var.set("Starting scraper...")

        # Start scraping in a separate thread
        self.scraping_thread = threading.Thread(target=self.scrape_data, daemon=True)
        self.scraping_thread.start()

    def stop_scraping(self):
        """Stop the scraping process"""
        self.is_scraping = False
        self.scrape_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.status_var.set("Scraping stopped by user")

    def clear_data(self):
        """Clear all scraped data"""
        self.scraped_data = {
            'event_info': {},
            'matches': [],
            'player_stats': [],
            'team_stats': []
        }

        # Clear UI displays
        self.event_text.delete(1.0, tk.END)
        self.team_text.delete(1.0, tk.END)

        # Clear treeviews
        for item in self.matches_tree.get_children():
            self.matches_tree.delete(item)
        for item in self.players_tree.get_children():
            self.players_tree.delete(item)

        self.stats_var.set("")
        self.progress_var.set(0)
        self.status_var.set("Data cleared - Ready to scrape...")

    def scrape_data(self):
        """Main scraping function - runs in separate thread"""
        try:
            url = self.url_var.get().strip()

            # Update status
            self.root.after(0, lambda: self.status_var.set("Fetching event information..."))
            self.root.after(0, lambda: self.progress_var.set(10))

            # Get event info
            event_info = self.scrape_event_info(url)
            if not self.is_scraping:
                return

            self.scraped_data['event_info'] = event_info
            self.root.after(0, self.update_event_display)

            # Get matches URL
            matches_url = url.replace('/event/', '/event/matches/')

            self.root.after(0, lambda: self.status_var.set("Fetching matches..."))
            self.root.after(0, lambda: self.progress_var.set(30))

            # Scrape matches
            matches = self.scrape_matches(matches_url)
            if not self.is_scraping:
                return

            self.scraped_data['matches'] = matches
            self.root.after(0, self.update_matches_display)

            self.root.after(0, lambda: self.status_var.set("Fetching player statistics..."))
            self.root.after(0, lambda: self.progress_var.set(60))

            # Scrape player stats from individual matches
            player_stats = self.scrape_player_stats(matches)
            if not self.is_scraping:
                return

            self.scraped_data['player_stats'] = player_stats
            self.root.after(0, self.update_player_stats_display)

            self.root.after(0, lambda: self.status_var.set("Calculating team statistics..."))
            self.root.after(0, lambda: self.progress_var.set(90))

            # Calculate team stats
            team_stats = self.calculate_team_stats()
            self.scraped_data['team_stats'] = team_stats
            self.root.after(0, self.update_team_stats_display)

            # Complete
            self.root.after(0, lambda: self.progress_var.set(100))
            self.root.after(0, lambda: self.status_var.set("✅ Scraping completed successfully!"))

            # Update statistics
            total_matches = len(self.scraped_data['matches'])
            total_players = len(self.scraped_data['player_stats'])
            stats_text = f"📊 {total_matches} matches, {total_players} player records"
            self.root.after(0, lambda: self.stats_var.set(stats_text))

        except Exception as e:
            error_msg = f"❌ Error during scraping: {str(e)}"
            self.root.after(0, lambda: self.status_var.set(error_msg))
            messagebox.showerror("Scraping Error", str(e))
        finally:
            self.is_scraping = False
            self.root.after(0, lambda: self.scrape_btn.config(state='normal'))
            self.root.after(0, lambda: self.stop_btn.config(state='disabled'))

    def scrape_event_info(self, url):
        """Scrape basic event information"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(url, headers=headers)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'html.parser')

        # Extract event information
        event_info = {
            'url': url,
            'scraped_at': datetime.now().isoformat()
        }

        # Event title
        title_elem = soup.find('h1', class_='wf-title')
        if title_elem:
            event_info['title'] = title_elem.get_text(strip=True)

        # Event dates and location
        event_desc = soup.find('div', class_='event-desc')
        if event_desc:
            desc_text = event_desc.get_text(strip=True)
            event_info['description'] = desc_text

            # Try to extract dates and location
            lines = desc_text.split('\n')
            for line in lines:
                line = line.strip()
                if any(month in line for month in ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                                                  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']):
                    event_info['dates'] = line
                elif any(word in line.lower() for word in ['korea', 'spain', 'china', 'usa', 'brazil']):
                    event_info['location'] = line

        # Prize pool
        prize_elem = soup.find('div', class_='prize-pool')
        if prize_elem:
            event_info['prize_pool'] = prize_elem.get_text(strip=True)

        return event_info

    def scrape_matches(self, matches_url):
        """Scrape all matches from the event"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(matches_url, headers=headers)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'html.parser')

        matches = []
        match_containers = soup.find_all('a', class_='wf-module-item')

        for container in match_containers:
            if not self.is_scraping:
                break

            match_data = self.extract_match_details(container)
            if match_data:
                matches.append(match_data)

        return matches

    def extract_match_details(self, container):
        """Extract match details from a match container"""
        try:
            # Get match URL
            match_url = 'https://www.vlr.gg' + container.get('href', '')

            # Extract team names
            team_elements = container.find_all('div', class_='match-item-vs-team-name')
            if len(team_elements) < 2:
                return None

            team1 = team_elements[0].get_text(strip=True)
            team2 = team_elements[1].get_text(strip=True)

            # Extract scores
            score_elements = container.find_all('div', class_='match-item-vs-team-score')
            score1 = score_elements[0].get_text(strip=True) if len(score_elements) > 0 else ''
            score2 = score_elements[1].get_text(strip=True) if len(score_elements) > 1 else ''

            # Extract stage/event info
            event_elements = container.find_all('div', class_='match-item-event')
            stage = event_elements[0].get_text(strip=True) if event_elements else 'Unknown Stage'

            # Extract time
            time_elements = container.find_all('div', class_='match-item-time')
            match_time = time_elements[0].get_text(strip=True) if time_elements else 'Time TBD'

            # Determine status
            status = 'Completed' if score1 and score2 and score1.isdigit() and score2.isdigit() else 'Scheduled'

            return {
                'team1': team1,
                'team2': team2,
                'score1': score1,
                'score2': score2,
                'stage': stage,
                'time': match_time,
                'status': status,
                'match_url': match_url,
                'scraped_at': datetime.now().isoformat()
            }

        except Exception as e:
            print(f"Error extracting match details: {e}")
            return None

    def scrape_player_stats(self, matches):
        """Scrape player statistics from individual matches"""
        player_stats = []

        # Only scrape stats from completed matches
        completed_matches = [m for m in matches if m['status'] == 'Completed']

        for i, match in enumerate(completed_matches[:10]):  # Limit to first 10 matches to avoid overloading
            if not self.is_scraping:
                break

            try:
                # Update progress
                progress = 60 + (i / len(completed_matches[:10])) * 25
                self.root.after(0, lambda p=progress: self.progress_var.set(p))

                match_stats = self.scrape_match_player_stats(match['match_url'])
                player_stats.extend(match_stats)

                # Small delay to be respectful to the server
                time.sleep(1)

            except Exception as e:
                print(f"Error scraping player stats for match {match['match_url']}: {e}")
                continue

        return player_stats

    def scrape_match_player_stats(self, match_url):
        """Scrape player statistics from a specific match"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        try:
            response = requests.get(match_url, headers=headers, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            player_stats = []

            # Look for player stats tables
            stats_tables = soup.find_all('table', class_='wf-table-inset')

            for table in stats_tables:
                rows = table.find_all('tr')

                for row in rows[1:]:  # Skip header row
                    cells = row.find_all('td')
                    if len(cells) >= 8:  # Ensure we have enough columns

                        # Extract player name
                        player_elem = cells[0].find('div', class_='text-of')
                        if not player_elem:
                            continue

                        player_name = player_elem.get_text(strip=True)

                        # Extract team (from team logo or context)
                        team_elem = row.find('img', class_='team-logo')
                        team = team_elem.get('alt', 'Unknown') if team_elem else 'Unknown'

                        # Extract stats (ACS, K, D, A, +/-, ADR, HS%, FK, FD)
                        try:
                            stats = {
                                'player': player_name,
                                'team': team,
                                'match_url': match_url,
                                'acs': cells[1].get_text(strip=True) if len(cells) > 1 else '0',
                                'kills': cells[2].get_text(strip=True) if len(cells) > 2 else '0',
                                'deaths': cells[3].get_text(strip=True) if len(cells) > 3 else '0',
                                'assists': cells[4].get_text(strip=True) if len(cells) > 4 else '0',
                                'plus_minus': cells[5].get_text(strip=True) if len(cells) > 5 else '0',
                                'adr': cells[6].get_text(strip=True) if len(cells) > 6 else '0',
                                'hs_percent': cells[7].get_text(strip=True) if len(cells) > 7 else '0%',
                                'first_kills': cells[8].get_text(strip=True) if len(cells) > 8 else '0',
                                'first_deaths': cells[9].get_text(strip=True) if len(cells) > 9 else '0',
                                'scraped_at': datetime.now().isoformat()
                            }

                            # Calculate K/D ratio
                            try:
                                k = int(stats['kills'])
                                d = int(stats['deaths'])
                                stats['kd_ratio'] = f"{k/d:.2f}" if d > 0 else "∞"
                            except:
                                stats['kd_ratio'] = "0.00"

                            player_stats.append(stats)

                        except Exception as e:
                            print(f"Error parsing player stats row: {e}")
                            continue

            return player_stats

        except Exception as e:
            print(f"Error scraping match player stats: {e}")
            return []

    def calculate_team_stats(self):
        """Calculate team statistics from player data"""
        team_stats = {}

        for player in self.scraped_data['player_stats']:
            team = player['team']
            if team not in team_stats:
                team_stats[team] = {
                    'team': team,
                    'total_players': 0,
                    'total_kills': 0,
                    'total_deaths': 0,
                    'total_assists': 0,
                    'total_acs': 0,
                    'matches_played': set()
                }

            try:
                team_stats[team]['total_players'] += 1
                team_stats[team]['total_kills'] += int(player['kills'])
                team_stats[team]['total_deaths'] += int(player['deaths'])
                team_stats[team]['total_assists'] += int(player['assists'])
                team_stats[team]['total_acs'] += int(player['acs'])
                team_stats[team]['matches_played'].add(player['match_url'])
            except ValueError:
                continue

        # Calculate averages
        for team, stats in team_stats.items():
            matches_count = len(stats['matches_played'])
            if matches_count > 0:
                stats['avg_kills_per_match'] = stats['total_kills'] / matches_count
                stats['avg_deaths_per_match'] = stats['total_deaths'] / matches_count
                stats['avg_acs_per_match'] = stats['total_acs'] / matches_count
                stats['kd_ratio'] = stats['total_kills'] / stats['total_deaths'] if stats['total_deaths'] > 0 else float('inf')

            # Convert set to count
            stats['matches_played'] = matches_count

        return list(team_stats.values())

    def update_event_display(self):
        """Update the event information display"""
        event_info = self.scraped_data['event_info']

        display_text = "🏆 EVENT INFORMATION\n"
        display_text += "=" * 50 + "\n\n"

        if 'title' in event_info:
            display_text += f"📋 Title: {event_info['title']}\n\n"

        if 'dates' in event_info:
            display_text += f"📅 Dates: {event_info['dates']}\n\n"

        if 'location' in event_info:
            display_text += f"📍 Location: {event_info['location']}\n\n"

        if 'prize_pool' in event_info:
            display_text += f"💰 Prize Pool: {event_info['prize_pool']}\n\n"

        if 'description' in event_info:
            display_text += f"📝 Description:\n{event_info['description']}\n\n"

        display_text += f"🔗 URL: {event_info['url']}\n"
        display_text += f"🕒 Scraped: {event_info['scraped_at']}\n"

        self.event_text.delete(1.0, tk.END)
        self.event_text.insert(1.0, display_text)

    def update_matches_display(self):
        """Update the matches display"""
        # Clear existing items
        for item in self.matches_tree.get_children():
            self.matches_tree.delete(item)

        # Add matches to treeview
        for match in self.scraped_data['matches']:
            score_display = f"{match['score1']}-{match['score2']}" if match['score1'] and match['score2'] else "TBD"

            self.matches_tree.insert('', 'end', values=(
                match['team1'],
                score_display,
                match['team2'],
                match['stage'],
                match['time'],
                match['status']
            ))

    def update_player_stats_display(self):
        """Update the player statistics display"""
        # Clear existing items
        for item in self.players_tree.get_children():
            self.players_tree.delete(item)

        # Add player stats to treeview
        for player in self.scraped_data['player_stats']:
            self.players_tree.insert('', 'end', values=(
                player['player'],
                player['team'],
                player['acs'],
                player['kills'],
                player['deaths'],
                player['assists'],
                player['kd_ratio'],
                player['adr'],
                player['hs_percent'],
                player['first_kills'],
                player['first_deaths']
            ))

    def update_team_stats_display(self):
        """Update the team statistics display"""
        team_stats = self.scraped_data['team_stats']

        display_text = "🏅 TEAM STATISTICS\n"
        display_text += "=" * 50 + "\n\n"

        # Sort teams by K/D ratio
        sorted_teams = sorted(team_stats, key=lambda x: x.get('kd_ratio', 0), reverse=True)

        for i, team in enumerate(sorted_teams, 1):
            display_text += f"{i}. {team['team']}\n"
            display_text += f"   📊 Matches Played: {team['matches_played']}\n"
            display_text += f"   🎯 Total Kills: {team['total_kills']}\n"
            display_text += f"   💀 Total Deaths: {team['total_deaths']}\n"
            display_text += f"   🤝 Total Assists: {team['total_assists']}\n"
            display_text += f"   ⚡ Avg ACS: {team.get('avg_acs_per_match', 0):.1f}\n"
            display_text += f"   📈 K/D Ratio: {team.get('kd_ratio', 0):.2f}\n"
            display_text += f"   👥 Players: {team['total_players']}\n"
            display_text += "\n" + "-" * 40 + "\n\n"

        self.team_text.delete(1.0, tk.END)
        self.team_text.insert(1.0, display_text)

    def export_json(self):
        """Export data to JSON file"""
        if not self.scraped_data['matches']:
            messagebox.showwarning("Warning", "No data to export!")
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            title="Save JSON Export"
        )

        if filename:
            try:
                # Prepare export data
                export_data = {
                    'export_info': {
                        'exported_at': datetime.now().isoformat(),
                        'source_url': self.scraped_data['event_info'].get('url', ''),
                        'total_matches': len(self.scraped_data['matches']),
                        'total_player_records': len(self.scraped_data['player_stats'])
                    },
                    'event_info': self.scraped_data['event_info'],
                    'matches': self.scraped_data['matches'],
                    'player_stats': self.scraped_data['player_stats'],
                    'team_stats': self.scraped_data['team_stats']
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("Success", f"Data exported to {filename}")

            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export JSON: {str(e)}")

    def export_csv(self):
        """Export data to CSV files"""
        if not self.scraped_data['matches']:
            messagebox.showwarning("Warning", "No data to export!")
            return

        # Ask for directory to save CSV files
        directory = filedialog.askdirectory(title="Select Directory for CSV Export")

        if directory:
            try:
                # Export matches
                matches_file = os.path.join(directory, "matches.csv")
                with open(matches_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=['team1', 'team2', 'score1', 'score2', 'stage', 'time', 'status', 'match_url'])
                    writer.writeheader()
                    writer.writerows(self.scraped_data['matches'])

                # Export player stats
                if self.scraped_data['player_stats']:
                    players_file = os.path.join(directory, "player_stats.csv")
                    with open(players_file, 'w', newline='', encoding='utf-8') as f:
                        fieldnames = list(self.scraped_data['player_stats'][0].keys())
                        writer = csv.DictWriter(f, fieldnames=fieldnames)
                        writer.writeheader()
                        writer.writerows(self.scraped_data['player_stats'])

                # Export team stats
                if self.scraped_data['team_stats']:
                    teams_file = os.path.join(directory, "team_stats.csv")
                    with open(teams_file, 'w', newline='', encoding='utf-8') as f:
                        fieldnames = list(self.scraped_data['team_stats'][0].keys())
                        writer = csv.DictWriter(f, fieldnames=fieldnames)
                        writer.writeheader()
                        writer.writerows(self.scraped_data['team_stats'])

                messagebox.showinfo("Success", f"CSV files exported to {directory}")

            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export CSV: {str(e)}")

    def copy_summary(self):
        """Copy summary to clipboard"""
        if not self.scraped_data['matches']:
            messagebox.showwarning("Warning", "No data to copy!")
            return

        summary = f"VLR.gg Event Data Summary\n"
        summary += f"========================\n\n"

        event_info = self.scraped_data['event_info']
        if 'title' in event_info:
            summary += f"Event: {event_info['title']}\n"
        if 'dates' in event_info:
            summary += f"Dates: {event_info['dates']}\n"
        if 'location' in event_info:
            summary += f"Location: {event_info['location']}\n"

        summary += f"\nStatistics:\n"
        summary += f"- Total Matches: {len(self.scraped_data['matches'])}\n"
        summary += f"- Player Records: {len(self.scraped_data['player_stats'])}\n"
        summary += f"- Teams: {len(self.scraped_data['team_stats'])}\n"

        # Copy to clipboard
        self.root.clipboard_clear()
        self.root.clipboard_append(summary)

        messagebox.showinfo("Success", "Summary copied to clipboard!")

def main():
    """Main function to run the application"""
    root = tk.Tk()

    # Configure ttk style
    style = ttk.Style()
    style.theme_use('clam')

    app = VLRScraperUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
